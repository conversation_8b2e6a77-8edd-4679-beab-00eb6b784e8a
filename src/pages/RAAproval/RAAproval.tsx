import React, {useEffect, useMemo, useRef, useState} from 'react';
import {useNavigate, useParams} from 'react-router-dom';
import {toast} from 'react-toastify';
import * as _ from 'lodash';
import PreviewFormDetails from '../CreateRA/PreviewFormDetails';
import {RiskForm} from '../../types';
import {TemplateForm} from '../../types/template';
import {useQuery} from '../../hooks/useQuery';
import {
  getRiskById,
  getTemplateById,
  setRiskRaLevel,
  updateSavedRA,
  getHazardsList,
  getMainRiskParameterType,
  getRiskCategoryList,
  getRiskParameterType,
  getTaskReliabilityAssessList,
} from '../../services/services';
import {createRiskFormFromData, formParameterHandler} from '../../utils/helper';
import SearchDropdown from '../../components/SearchDropdown';
import {raLevels} from '../RAListing/components/RAFilters';
import {RaLevel, RAStatus, TemplateFormStatus} from '../../enums';
import RAApprovalModal from '../CreateRA/RAApprovalModal';
import {
  getErrorMessage,
  parseDate,
  raStatusLabelToValue,
} from '../../utils/common';
import Loader from '../../components/Loader';

import '../../styles/components/ra-approval.scss';
import {useDataStoreContext} from '../../context';

export default function RAAproval() {
  const params = useParams<{id: string}>();
  const raId = String(params.id);

  const {
    setDataStore,
    roleConfig,
    roleConfig: {user},
  } = useDataStoreContext();
  const [isLoading, setIsLoading] = useState(true);
  const [form, setForm] = useState<RiskForm | null>(null);
  const [originalForm, setOriginalForm] = useState<RiskForm | null>(null);
  const [levelOfRA, setLevelOfRA] = useState<RaLevel | undefined>(undefined);
  const atRiskRef = useRef<any>(null);

  const navigate = useNavigate();

  // Function to check if specific fields have changed
  const hasFormChanges = useMemo(() => {
    if (!form || !originalForm) return false;

    // Check the specific fields mentioned in the requirements
    const fieldsToCheck = [
      'task_alternative_consideration',
      'task_rejection_reason',
      'worst_case_scenario',
      'recovery_measures',
    ];

    // Check if any of the text fields have changed
    const textFieldsChanged = fieldsToCheck.some(
      field =>
        form[field as keyof RiskForm] !== originalForm[field as keyof RiskForm],
    );

    // Check if risk_task_reliability_assessment has changed
    const assessmentChanged = !_.isEqual(
      form.risk_task_reliability_assessment,
      originalForm.risk_task_reliability_assessment,
    );

    return textFieldsChanged || assessmentChanged;
  }, [form, originalForm]);

  const previewOnly = useMemo(() => {
    if (!form) return true;

    const isPublished =
      raStatusLabelToValue[form.status] === RAStatus.PUBLISHED;
    const canApprove = roleConfig?.riskAssessment?.canApproveRisk === true;

    if (!isPublished || !canApprove) {
      return true;
    }

    const pendingApprovers = form.risk_approver?.filter(
      approver => approver.approval_status === null,
    );

    const isUserOneOfTheApprovers = pendingApprovers?.some(
      approver => approver.keycloak_id === user?.user_id,
    );

    return !isUserOneOfTheApprovers;
  }, [form]);

  const {
    data: raData,
    isLoading: isLoadingRa,
    isError,
    error,
    refetch,
  } = useQuery(['risk', raId], () => getRiskById(raId), {
    enabled: !!raId,
    onSuccess: data => {
      if (data?.result) {
        const formData = createRiskFormFromData(data.result);
        setForm(formData);
        setOriginalForm(_.cloneDeep(formData));
      }
    },
  });

  const templateId = raData?.result.template_id;
  const templateQ = useQuery(
    ['template', templateId],
    () => getTemplateById(String(templateId)),
    {enabled: Boolean(templateId)},
  );

  useEffect(() => {
    const loadBasicDetails = async () => {
      try {
        setIsLoading(true);
        const results = await Promise.allSettled([
          getRiskCategoryList(),
          getHazardsList(),
          getRiskParameterType(),
          getTaskReliabilityAssessList(),
          getMainRiskParameterType(),
          getMainRiskParameterType(true),
        ]);
        const [
          categorListData,
          hazardsListData,
          riskParameterData,
          taskRelAssessData,
          mainRiskParameterData,
          mainRiskParameterDataForRiskRating,
        ] = results.map(result =>
          result.status === 'fulfilled' ? result.value : [],
        );

        const groupedRiskParameterData = _.chain(riskParameterData)
          .groupBy(item => item?.parameter_type?.id)
          .map(items => ({
            id: items[0]?.parameter_type?.id,
            name: items[0]?.parameter_type?.name,
            parameters: items?.map(i => ({
              id: i?.id,
              name: i?.name,
            })),
          }))
          .value();

        setDataStore((prev: any) => ({
          ...prev,
          riskCategoryList: categorListData,
          hazardsList: hazardsListData,
          riskParameterType: groupedRiskParameterData,
          taskReliabilityAssessList: taskRelAssessData,
          riskParameterList: mainRiskParameterData,
          riskParameterListForRiskRaiting: mainRiskParameterDataForRiskRating,
        }));
      } catch (error) {
        console.error('Error loading data:', error);
      } finally {
        setIsLoading(false);
      }
    };
    loadBasicDetails();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const $content = useMemo(() => {
    if (isLoadingRa || isLoading) {
      return <Loader isOverlayLoader />;
    }

    if (isError) {
      return <div>Error: {error?.message || 'Failed to load data.'}</div>;
    }

    if (!form) {
      return <div>No data found.</div>;
    }

    return null;
  }, [isLoadingRa, isLoading, isError, error, form]);

  const setRaLevel = async (
    risk_id: number,
    ra_level: RaLevel,
    actionDate?: Date,
  ) => {
    if (!levelOfRA || !raId || (levelOfRA === RaLevel.ROUTINE && !actionDate)) {
      toast.info('Please select a valid RA level and action date.');
      return;
    }
    setForm(prev => ({...prev, ra_level: levelOfRA} as RiskForm));
    try {
      const result = await setRiskRaLevel({
        ra_level,
        risk_id,
        approval_date: parseDate(actionDate) || undefined,
      });

      refetch();
      return result;
    } catch (error) {
      toast.error(getErrorMessage(error));
      throw error;
    }
  };

  const updateRaDetails = async (formToSave?: RiskForm | TemplateForm) => {
    setIsLoading(true);
    const formData = formToSave || form;
    try {
      await updateSavedRA(
        Number(raId),
        formParameterHandler({
          ...formData,
        }),
      );
      toast.success(`Risk form updated successfully`);
      // Update the original form to reflect the saved state
      setOriginalForm(_.cloneDeep(formData as RiskForm));
    } catch (err) {
      console.error('Error publishing template:', err);
    } finally {
      setIsLoading(false);
    }
    refetch();
  };

  const isCategoriesSameAsTemplate =
    (raData?.result?.risk_category?.length ?? 0) > 0
      ? templateQ.data?.result?.template_category?.every(templateCategory =>
          raData?.result?.risk_category.some(
            riskCategory =>
              riskCategory.category.id === templateCategory.category?.id,
          ),
        )
      : false;

  const isHazardsSameAsTemplate =
    (raData?.result?.risk_hazards?.length ?? 0) > 0
      ? templateQ.data?.result?.template_hazards?.every(templateHazard =>
          raData?.result?.risk_hazards.some(
            riskHazard =>
              riskHazard?.hazard_detail?.id ===
              templateHazard?.hazard_detail?.id,
          ),
        )
      : false;

  const defaultRiskApprover = raData?.result?.risk_approver?.find(
    approver => approver.status === 0 && approver.approval_order === null,
  );
  const showLevelOfRADropdown =
    raData &&
    !raData.result.ra_level &&
    defaultRiskApprover?.keycloak_id === user.user_id;

  // const handleFormPublish = async () => {
  //   setIsLoading(true);
  //   if (!form) return;
  //   const payload = {
  //     ...form,
  //     status: TemplateFormStatus.PUBLISHED,
  //   };
  //   await formParameterHandler(payload);

  //   try {
  //     await updateSavedRA(Number(raId), payload);
  //     toast.success(`Risk form updated successfully`);
  //   } catch (err) {
  //     console.error('Error publishing template:', err);
  //   } finally {
  //     setIsLoading(false);
  //   }
  // };

  return (
    <div className="ra-approval-page">
      {$content || (
        <PreviewFormDetails
          raId={Number(raId)}
          type="risk"
          form={form as unknown as RiskForm}
          setForm={setForm}
          atRiskRef={atRiskRef}
          handlePreviewPublush={updateRaDetails}
          handleSaveToDraft={() => {}}
          isCategoriesSameAsTemplate={isCategoriesSameAsTemplate}
          isHazardsSameAsTemplate={isHazardsSameAsTemplate}
          riskApprover={raData?.result?.risk_approver}
          refechRA={refetch}
          breadcrumbOptions={{
            items: [
              {title: 'Risk Assessment', link: '/risk-assessment'},
              {title: form?.task_requiring_ra || ''}, // No link, just text
            ],
            options: showLevelOfRADropdown ? (
              <div className="d-flex align-items-center justify-content-end">
                <SearchDropdown
                  placeholder="Set Level of R.A."
                  className="ra-approval-status-dropdown"
                  options={raLevels.filter(
                    item => item.value !== RaLevel.LEVEL_1_RA,
                  )}
                  selected={levelOfRA ? [levelOfRA] : null}
                  onChange={value => {
                    console.log('Selected value:', value);
                    if (value && value.length > 0) {
                      setLevelOfRA(Number(value[0]));
                    } else {
                      setLevelOfRA(undefined);
                    }
                  }}
                  multiple={false}
                />
                {levelOfRA === RaLevel.ROUTINE ? (
                  <RAApprovalModal
                    operationType="approve"
                    trigger={
                      <button
                        disabled={!levelOfRA}
                        className="ra-approval-save-btn"
                      >
                        Save
                      </button>
                    }
                    onConfirm={params =>
                      setRaLevel(Number(raId), levelOfRA, params.actionDate)
                    }
                  />
                ) : (
                  <button
                    disabled={!levelOfRA}
                    className="ra-approval-save-btn"
                    onClick={() =>
                      setRaLevel(Number(raId), levelOfRA as unknown as RaLevel)
                    }
                  >
                    Save
                  </button>
                )}
              </div>
            ) : undefined,
          }}
          bottomButtonConfig={[
            {
              title: 'Cancel',
              testID: 'form-ra-approval-cancel-btn',
              variant: 'secondary',
              customClass: 'sec-btn fs-14',
              onClick: () => {
                navigate('/risk-assessment');
              },
            },
            {
              title: 'Save',
              testID: 'form-ra-approval-save-btn',
              variant: 'primary',
              customClass: 'primary-btn fs-14',
              onClick: async () => {
                await updateRaDetails();
              },
              disabled: isLoading || !hasFormChanges,
            },
          ]}
          previewOnly={previewOnly}
          showBreadCrumb
          allowSaveOnModalClose
        />
      )}
    </div>
  );
}
