import React, {useRef, useState, useEffect} from 'react';
import {<PERSON><PERSON>, But<PERSON>} from 'react-bootstrap';
import {cloneDeep} from 'lodash';
import {TemplateForm, TemplateFormJob} from '../../types/template';
import {RiskForm, RiskFormJob} from '../../types/risk';
import {AddJobsStep} from './AddJobsStep';
import {v4 as uuidv4} from 'uuid';

type Props = {
  onClose: (shouldSave?: boolean) => void;
  form: TemplateForm | RiskForm;
  setForm: (f: any) => void;
  type?: 'template' | 'risk';
};

const emptyTemplateJob: TemplateFormJob = {
  job_id: '', // You may want to generate a uuid here
  job_step: '',
  job_hazard: '',
  job_nature_of_risk: '',
  job_existing_control: '',
  job_additional_mitigation: '',
  job_close_out_date: '',
  job_close_out_responsibility_id: '',
  template_job_initial_risk_rating: [],
  template_job_residual_risk_rating: [],
};

const emptyRiskJob: RiskFormJob = {
  job_step: '',
  job_hazard: '',
  job_nature_of_risk: '',
  job_existing_control: '',
  job_additional_mitigation: '',
  job_close_out_date: '',
  job_close_out_responsibility_id: '',
  risk_job_initial_risk_rating: [],
  risk_job_residual_risk_rating: [],
};

export const AddJobModal: React.FC<Props> = ({
  onClose,
  form,
  setForm,
  type = 'template',
}) => {
  const addJobsRef = useRef<any>(null);

  // For add mode, initialize with a single empty job
  const [clonedForm, setClonedForm] = useState<TemplateForm | RiskForm>(() => {
    const cloned = cloneDeep(form);
    if (type === 'risk') {
      // Add mode: start with a single empty job for risk form
      (cloned as RiskForm).risk_job = [{...emptyRiskJob}];
    } else {
      // Add mode: start with a single empty job for template form
      (cloned as TemplateForm).template_job = [{...emptyTemplateJob}];
    }
    return cloned;
  });

  const [isFormValid, setIsFormValid] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);

  // Track changes for enabling Save button
  useEffect(() => {
    // Add mode: check if any field is filled
    let job: TemplateFormJob | RiskFormJob | undefined;
    if (type === 'risk') {
      job = (clonedForm as RiskForm).risk_job[0];
    } else {
      job = (clonedForm as TemplateForm).template_job[0];
    }

    const filled =
      job &&
      (job.job_step ||
        job.job_hazard ||
        job.job_nature_of_risk ||
        job.job_existing_control ||
        job.job_additional_mitigation);
    setHasChanges(!!filled);
  }, [form, clonedForm, type]);

  // Validation from child
  const handleValidation = (valid: boolean) => {
    setIsFormValid(valid);
  };
  const handleSave = async () => {
    if (!addJobsRef.current.validate()) return;

    const addRiskJob = () => {
      const riskForm = clonedForm as RiskForm;
      if (riskForm.risk_job.length === 0) return;
      const newJob = {...riskForm.risk_job[0]};
      setForm((prev: RiskForm) => ({
        ...prev,
        risk_job: [...(prev.risk_job || []), newJob],
      }));
    };

    const addTemplateJob = () => {
      const templateForm = clonedForm as TemplateForm;
      if (templateForm.template_job.length === 0) return;
      const newJob = {...templateForm.template_job[0]};
      if (!newJob.job_id) {
        newJob.job_id = uuidv4();
      }
      setForm((prev: TemplateForm) => ({
        ...prev,
        template_job: [...(prev.template_job || []), newJob],
      }));
    };

    if (type === 'risk') {
      addRiskJob();
    } else {
      addTemplateJob();
    }
    onClose(true);
  };

  return (
    <Modal
      show
      onHide={() => onClose(false)}
      size="xl"
      backdrop="static"
      dialogClassName="top-modal"
    >
      <Modal.Header>
        <Modal.Title>{'Add Associated Job'}</Modal.Title>
      </Modal.Header>
      <Modal.Body className="edit-modal-body">
        <AddJobsStep
          ref={addJobsRef}
          form={clonedForm}
          setForm={setClonedForm}
          onValidate={handleValidation}
          isEdit={true}
          jobIndex={0}
          type={type}
        />
      </Modal.Body>
      <Modal.Footer>
        <Button
          variant="primary"
          className="me-2 fs-14"
          onClick={() => onClose(false)}
        >
          Cancel
        </Button>
        <Button
          variant="secondary"
          className="me-2 fs-14"
          onClick={handleSave}
          disabled={!isFormValid || !hasChanges}
        >
          Save Changes
        </Button>
      </Modal.Footer>
    </Modal>
  );
};
