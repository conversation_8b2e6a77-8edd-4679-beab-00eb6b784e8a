import React, {useState, useEffect, useMemo} from 'react';
import {toast} from 'react-toastify';
import * as _ from 'lodash';
import {RiskCategory, RiskParameter, useDataStoreContext} from '../../context';
import {Row, Col, Button, Badge, Card} from 'react-bootstrap';
import InfiniteScrollTable from '../../components/InfiniteScrollTable';
import {EditFormDetailIcon} from '../../utils/svgIcons';
import {RiskRatingStep} from './RiskRatingStep';
import BottomButton, {BottomButtonProps} from '../../components/BottomButton';
import ProjectBreadCrumb, {
  ProjectBreadCrumbProps,
} from '../../components/ProjectBreadCrumb';
import {InputComponent} from '../../components/InputComponent';
import {TemplateForm} from '../../types/template';
import {RAItemFull, RiskForm, RiskJob} from '../../types/risk';
import {EditTemplateModal} from '../../components/EditTemplateModal';
import {DeleteJobModal} from '../../components/DeleteJobModal';
import {AddJobModal} from './AddJobModal';
import {UsernameProfile} from '../../components/UsernameProfile';
import {format} from 'date-fns';
import {
  generatePDF,
  getApprovalsRequiredList,
  getMainRiskParameterType,
  getOfficesList,
  getRiskParameterType,
  getVesselsList,
} from '../../services/services';

import GuidancePdf from '../../../public/GuidPdf.pdf';
import RiskMatrixPdf from '../../../public/RiskMatrixPdf.pdf';
import {
  calculateRiskRating,
  createGroupedVesselOfficeOptions,
  findSelectedVesselOfficeOption,
  getRiskRatingBackgroundColor,
  getRiskRatingTextColor,
  GroupedVesselOfficeOption,
  groupRiskParameters,
} from '../../utils/helper';
import {RiskJobCell} from '../../components/RiskJobCell';
import {ActionsDropdownCell} from '../../components/ActionsDropdownCell';
import {
  raLevelColor,
  raLevelLabel,
  raStatusLabelToValue,
  vesselStatusAndLabelName,
} from '../../utils/common';
import ColoredTile from '../../components/ColoredTile';
import AddApproverCard from './AddApproverCard';
import {RaLevel} from '../../enums';

import '../../styles/components/preview-form-details.scss';
import Loader from '../../components/Loader';
import SubmitLevel1RAModal from './SubmitLevel1RAModal';
import {useNavigate, useParams} from 'react-router-dom';

const renderRiskJobCell = (type: 'risk' | 'template') => (info: any) =>
  <RiskJobCell type={type} original={info.row.original} />;

const renderActionsDropdownCell =
  (
    setEditStep: (step: number) => void,
    setEditModalTitle: (title: string) => void,
    setSelectedJobIdx: (id: string) => void,
    setIsEdit: (edit: boolean) => void,
    setShowDeleteJobModal: (show: boolean) => void,
  ) =>
  (info: any) =>
    (
      <ActionsDropdownCell
        jobId={info.row.original.job_id}
        setEditStep={setEditStep}
        setEditModalTitle={setEditModalTitle}
        setSelectedJobIdx={setSelectedJobIdx}
        setIsEdit={setIsEdit}
        setShowDeleteJobModal={setShowDeleteJobModal}
      />
    );

interface PreviewFormDetailsProps {
  raId?: number;
  form: TemplateForm | RiskForm;
  setForm: (f: any) => void;
  atRiskRef: React.RefObject<any>;
  handlePreviewPublush: (formToSave?: TemplateForm | RiskForm) => void;
  handleSaveToDraft: (step: number) => void;
  type?: 'template' | 'risk';
  previewOnly?: boolean;
  breadcrumbOptions?: ProjectBreadCrumbProps;
  isCategoriesSameAsTemplate?: boolean;
  isHazardsSameAsTemplate?: boolean;
  riskApprover?: RAItemFull['risk_approver'];
  refechRA?: () => void;
  isApprovedId?: string;
  disableFooterButtons?: boolean;
  bottomButtonConfig?: BottomButtonProps['buttons'];
  showBreadCrumb?: boolean;
  showUseTemplate?: boolean;
  allowSaveOnModalClose?: boolean;
}

const PreviewFormDetails: React.FC<PreviewFormDetailsProps> = ({
  raId,
  form,
  setForm,
  atRiskRef,
  handlePreviewPublush,
  handleSaveToDraft,
  breadcrumbOptions,
  type = 'template',
  previewOnly,
  isCategoriesSameAsTemplate = false,
  isHazardsSameAsTemplate = false,
  riskApprover,
  refechRA,
  isApprovedId,
  disableFooterButtons = false,
  bottomButtonConfig,
  showBreadCrumb = false,
  showUseTemplate = false,
  allowSaveOnModalClose = false,
}) => {
  const {
    dataStore: {
      riskCategoryList,
      hazardsList,
      riskParameterList = [],
      riskParameterType = [],
      approversReqListForRiskOffice,
      approversReqListForRiskVessel,
      vesselListForRisk,
      officeListForRisk,
    },
    roleConfig: {
      user: {user_id, name: userName},
    },
  } = useDataStoreContext();

  const navigate = useNavigate();
  const params = useParams<{id: string}>();

  const [isEdit, setIsEdit] = useState(false);
  const [editStep, setEditStep] = useState(0);
  const [editModalTitle, setEditModalTitle] = useState('');
  const [selectedJobIdx, setSelectedJobIdx] = useState('');
  const [tableKey, setTableKey] = useState(0); // Key to force table refresh
  const [showDeleteJobModal, setShowDeleteJobModal] = useState(false);
  const [showAddJobModal, setShowAddJobModal] = useState(false);
  const [showRASubmitModal, setShowRASubmitModal] = useState(false);
  const [approvalOptions, setApprovalOptions] = useState<
    {id: number; name: string}[]
  >([]);
  const [vesselOfficeName, setVesselOfficeName] = useState('');
  const [loader, setLoader] = useState(false);
  const [riskParametersListLoc, setRiskParametersListLoc] =
    useState<RiskCategory[]>(riskParameterList);
  console.log('🚀 ~ riskParametersListLoc:', riskParametersListLoc);
  const [riskParametersTypeLoc, setRiskParametersTypeLoc] =
    useState<RiskParameter[]>(riskParameterType);
  console.log('🚀 ~ riskParametersTypeLoc:', riskParametersTypeLoc);

  const [groupedVesselOfficeOptions, setGroupedVesselOfficeOptions] = useState<
    GroupedVesselOfficeOption[]
  >([]);

  // Helper functions to handle both form types
  const getJobsArray = () => {
    if (type === 'risk') {
      const riskJobs = (form as RiskForm).risk_job || [];
      // Add index-based job_id for risk forms to work with ActionsDropdownCell
      return riskJobs.map((job, index) => ({
        ...job,
        job_id: index.toString(), // Use index as job_id for risk forms
      }));
    }
    return (form as TemplateForm).template_job || [];
  };

  const getCategoryData = () => {
    if (type === 'risk') {
      return (form as RiskForm).risk_category || {};
    }
    return (form as TemplateForm).template_category || {};
  };

  const getHazardData = () => {
    if (type === 'risk') {
      return (form as RiskForm).risk_hazard || {};
    }
    return (form as TemplateForm).template_hazard || {};
  };

  // Monitor changes to job data and refresh table
  useEffect(() => {
    setTableKey(prev => prev + 1);
  }, [form, type]); // Depend on form and type instead of calling getJobsArray()

  // Load approval options for risk forms
  useEffect(() => {
    const fetchInitialData = async () => {
      try {
        setLoader(true);
        if (type === 'risk') {
          const vesselList = vesselListForRisk.length
            ? vesselListForRisk
            : await getVesselsList();
          const officeList = officeListForRisk.length
            ? officeListForRisk
            : await getOfficesList();
          const groupedOptions = createGroupedVesselOfficeOptions(
            vesselList,
            officeList,
            vesselStatusAndLabelName,
          );
          setGroupedVesselOfficeOptions(groupedOptions); // store this in state for reuse
        } else {
          if (
            riskParameterType.length === 0 &&
            riskParametersTypeLoc.length === 0
          ) {
            const riskParameterData = await getMainRiskParameterType();
            setRiskParametersListLoc(riskParameterData);
          }

          if (
            riskParameterList.length === 0 &&
            riskParametersListLoc.length === 0
          ) {
            const riskParameterData = await getRiskParameterType();
            const groupedRiskParameterData =
              groupRiskParameters(riskParameterData);
            setRiskParametersTypeLoc(groupedRiskParameterData);
          }
        }
      } catch (error) {
        console.error('Error loading initial data:', error);
      } finally {
        setLoader(false);
      }
    };

    fetchInitialData();
  }, [type]);

  useEffect(() => {
    const loadFormData = async () => {
      if (!form || type !== 'risk') return;

      try {
        setLoader(true);
        const assessor = (form as RiskForm).assessor;
        const ownershipId = (form as RiskForm).vessel_ownership_id;

        const approvalList =
          assessor === 2
            ? approversReqListForRiskVessel
            : approversReqListForRiskOffice;
        setApprovalOptions(approvalList);

        const name = findSelectedVesselOfficeOption(
          groupedVesselOfficeOptions,
          ownershipId,
        )?.label;

        setVesselOfficeName(name || '');
      } catch (error) {
        console.error('Error loading form-based risk data:', error);
      } finally {
        setLoader(false);
      }
    };

    loadFormData();
  }, [form, type, groupedVesselOfficeOptions]);

  const nameGenrator = (name: string) => {
    return (
      <Badge
        key={name}
        className="d-flex align-items-center py-2 px-2 badge-keyword"
        style={{fontSize: '14px', fontWeight: '400'}}
      >
        {name}
      </Badge>
    );
  };

  // Helper to get names from IDs, considering is_other/value
  const getNamesByIds = (
    list: any[],
    obj: {
      category_id?: number[];
      hazard_id?: number[];
      is_other?: boolean;
      value?: string;
    },
  ) => {
    const ids = obj?.category_id || obj?.hazard_id || [];
    let names = list
      .filter(item => ids?.includes(item.id))
      .map(item => item.name);
    if (obj?.is_other && obj?.value) {
      names = [...names, obj.value];
    }
    return names;
  };

  // Helper for At Risk, considering is_other/value
  const getAtRisk = (parameterTypeId: number) => {
    const param = (form.parameters || []).find(
      (p: any) => p.parameter_type_id === parameterTypeId,
    );
    let arr: (number | string)[] = param?.parameter_id?.length
      ? param.parameter_id
      : [];
    if (param?.is_other && param?.value) {
      arr = [...arr, param.value];
    }
    return _.uniq(arr);
  };

  // Table columns for InfiniteScrollTable
  const baseColumns = [
    {
      header: 'Sr. No.',
      accessorFn: (_row: any, idx: number) => idx + 1,
      id: 'srNo',
      cell: (info: any) => info.row.index + 1,
      meta: {
        isSticky: true,
        stickySide: 'custom-sticky-styles-no-border-right',
      },
      enableSorting: false,
      minSize: 65,
    },
    {
      header: 'Job Steps',
      accessorKey: 'job_step',
      meta: {isSticky: true, stickySide: 'left', headerAlign: 'center'},
      enableSorting: false,
      cell: renderRiskJobCell(type),
    },
    {
      header: 'Hazard',
      accessorKey: 'job_hazard',
      enableSorting: false,
    },
    {
      header: 'Nature of Risk',
      accessorKey: 'job_nature_of_risk',
      enableSorting: false,
    },
    {
      header: 'Existing Control',
      accessorKey: 'job_existing_control',
      enableSorting: false,
    },
    {
      header: 'Additional Mitigation',
      accessorKey: 'job_additional_mitigation',
      enableSorting: false,
    },
  ];

  // Action column for template forms only
  const actionColumn = {
    header: 'Action',
    id: 'action',
    cell: renderActionsDropdownCell(
      setEditStep,
      setEditModalTitle,
      setSelectedJobIdx,
      setIsEdit,
      setShowDeleteJobModal,
    ),

    meta: {
      isSticky: true,
      stickySide: 'right',
      headerAlign: 'center',
    },
    enableSorting: false,
  };

  // Combine columns based on form type and previewOnly prop
  const columns = !previewOnly ? [...baseColumns, actionColumn] : baseColumns;

  const onClose = async (
    shouldSave?: boolean,
    updatedForm?: TemplateForm | RiskForm,
  ) => {
    setIsEdit(false);
    if (editStep === 5) {
      // Force table refresh when closing step 5 (Hazard & Control Measures) editing
      setTableKey(prev => prev + 1);
    }
    setEditModalTitle('');
    setEditStep(0);
    setSelectedJobIdx('');

    // Only call handlePreviewPublush if user clicked Save and allowSaveOnModalClose is true
    if (shouldSave && allowSaveOnModalClose) {
      // Update the main form state
      if (updatedForm) {
        setForm(updatedForm);
        // Pass the updated form directly to handlePreviewPublush
        await handlePreviewPublush(updatedForm);
      } else {
        await handlePreviewPublush();
      }
    }
  };

  const onDeleteClose = async (shouldSave?: boolean) => {
    setShowDeleteJobModal(false);
    // Force table refresh when closing step 5 (Hazard & Control Measures) editing
    setTableKey(prev => prev + 1);
    setSelectedJobIdx('');
    // Only call handlePreviewPublush if user clicked Delete (which is a save action) and allowSaveOnModalClose is true
    if (shouldSave && allowSaveOnModalClose) {
      await handlePreviewPublush();
    }
  };

  const colMdVal = type === 'risk' ? 2 : 3;
  const showCommonDetails = type === 'template' || previewOnly;
  const riskRating = calculateRiskRating(form);

  const defaultBreadcrumbItems: ProjectBreadCrumbProps['items'] = [
    {title: 'Risk Assessment', link: '/risk-assessment'},
    {title: 'Drafts', link: '/risk-assessment/drafts'},
    {title: form.task_requiring_ra || ''}, // No link, just text
  ];

  const $duplicateCategoriesMessage = useMemo(() => {
    if (!(isCategoriesSameAsTemplate || isHazardsSameAsTemplate)) return null;

    let title = null;

    if (isCategoriesSameAsTemplate && isHazardsSameAsTemplate) {
      title = 'Risk & Hazard Categories';
    } else if (isCategoriesSameAsTemplate && !isHazardsSameAsTemplate) {
      title = 'Risk Categories';
    } else if (!isCategoriesSameAsTemplate && isHazardsSameAsTemplate) {
      title = 'Hazard Categories';
    }

    return (
      <div className="no-edits-text-red">
        <span>{title}</span>&nbsp;are the same as mentioned in Template. Check
        additional categories to be added if required.
      </div>
    );
  }, [isCategoriesSameAsTemplate, isHazardsSameAsTemplate]);

  const hasRiskAlert = getJobsArray()?.some(job => {
    return (job as unknown as RiskJob).risk_job_residual_risk_rating?.some(
      (rating: any) => rating?.reason?.length,
    );
  });

  const handelGenratePDF = async (id: number | string) => {
    try {
      toast.info(`Sending PDF to Your Registered Email`);
      setLoader(true);
      await generatePDF(id);
      setLoader(false);
      toast.success(`PDF Sent Successfully!`);
    } catch (error) {
      console.error('Error generating PDF:', error);
      toast.error(`Error Sending PDF to Your Registered Email`);
      setLoader(false);
    }
  };
  const handlePublish = () => {
    if (type === 'risk' && form?.ra_level === 4) setShowRASubmitModal(true);
    else handlePreviewPublush();
  };

  return (
    <div className="preview-form-div preview-form-details">
      {loader && <Loader isOverlayLoader />}
      <div style={{margin: '0px 0px 68px 0px'}}>
        {/* Header */}
        {(!previewOnly || showBreadCrumb) && (
          <div className="mb-4 d-flex justify-content-between align-items-center">
            <ProjectBreadCrumb
              {...breadcrumbOptions}
              items={breadcrumbOptions?.items || defaultBreadcrumbItems}
            />
            {!!isApprovedId && (
              <Button
                variant="outline-primary"
                className="me-2 basic-btn"
                onClick={() => {
                  handelGenratePDF(isApprovedId);
                }}
              >
                Export PDF
              </Button>
            )}
            {showUseTemplate && (
              <Button
                variant="primary"
                className="create-new-btn text-nowrap"
                onClick={() =>
                  navigate(
                    `/risk-assessment/templates/${params?.id}/risks/create`,
                  )
                }
              >
                Use this Template
              </Button>
            )}
          </div>
        )}

        {/* Basic Details */}
        <div
          className="mb-3"
          style={{
            background: '#fff',
            borderRadius: 6,
            border: '1px solid #CCCCCC',
            padding: 16,
          }}
        >
          <div className="d-flex justify-content-between align-items-center mb-2">
            <div style={{fontSize: 24, fontWeight: 700, color: '#1F4A70'}}>
              {form.task_requiring_ra || 'Task Title'}
              {type === 'risk' && (
                <div className="text-muted fs-14 fw-400">
                  Overall Risk Rating:{' '}
                  <span
                    style={{
                      backgroundColor: getRiskRatingBackgroundColor(riskRating),
                      color: getRiskRatingTextColor(riskRating),
                      borderRadius: '4px',
                      fontSize: '12px',
                      fontWeight: 500,
                      padding: '2px 8px',
                      alignSelf: 'end',
                      marginTop: '0.5rem',
                    }}
                  >
                    {riskRating}
                  </span>
                </div>
              )}
            </div>
            {!previewOnly && (
              <Button
                variant="link"
                className="underline"
                style={{
                  textDecoration: 'none',
                  fontWeight: 400,
                  fontSize: 14,
                  color: '#1F4A70',
                  display: 'flex',
                  alignItems: 'center',
                  gap: 4,
                }}
                onClick={() => {
                  setEditStep(1);
                  setEditModalTitle('Edit Basic Details');
                  setIsEdit(true);
                }}
              >
                <EditFormDetailIcon />
                Edit Basic Details
              </Button>
            )}
          </div>
          <hr style={{margin: '0 -20px 16px -20px', borderColor: '#DEE2E6'}} />
          <Row className="">
            {type === 'risk' && (
              <Col md={colMdVal}>
                <div className="fs-14 fw-600">Assessor</div>
                <div className="fs-14 fw-400">
                  {(form as RiskForm).assessor === 2 ? 'Vessel' : 'Office'}
                </div>
              </Col>
            )}
            {type === 'risk' && (
              <Col md={colMdVal}>
                <div className="fs-14 fw-600">Vessel/Office</div>
                <div className="fs-14 fw-400">{vesselOfficeName}</div>
              </Col>
            )}
            {type === 'risk' && (
              <Col md={colMdVal}>
                <div className="fs-14 fw-600">Date of Risk Assessment</div>
                <div className="fs-14 fw-400">
                  {' '}
                  {format(
                    new Date((form as RiskForm).date_risk_assessment),
                    'dd MMM yyyy',
                  )}
                </div>
              </Col>
            )}
            {type === 'risk' && (
              <Col md={colMdVal}>
                <div className="fs-14 fw-600">Level of R.A.</div>
                <div className="fs-14 fw-400">
                  {(form as RiskForm).ra_level ? (
                    <ColoredTile
                      text={raLevelLabel[form.ra_level as unknown as number]}
                      theme={raLevelColor[form.ra_level as unknown as number]}
                    />
                  ) : (
                    '---'
                  )}
                </div>
              </Col>
            )}
            <Col md={colMdVal}>
              <div style={{fontWeight: 600, fontSize: 14}}>
                Duration of Task
              </div>
              <div style={{fontSize: 14, fontWeight: 400}}>
                {form.task_duration ? form.task_duration : '-'}
              </div>
            </Col>
            {type === 'risk' && (
              <Col md={colMdVal}>
                <div className="fs-14 fw-600">Approvals Required</div>
                <div className="fs-14 fw-400">
                  {(form as RiskForm).approval_required?.length > 0
                    ? approvalOptions
                        .filter(opt =>
                          (form as RiskForm).approval_required?.includes(
                            opt.id,
                          ),
                        )
                        .map(opt => opt.name)
                        .join(', ') ||
                      (form as RiskForm).approval_required.length + ' selected'
                    : 'None'}
                </div>
              </Col>
            )}
            {showCommonDetails && (
              <Col md={colMdVal}>
                <div style={{fontWeight: 600, fontSize: 14}}>
                  Last Updated on
                </div>
                <div style={{fontSize: 14, fontWeight: 400}}>
                  {form?.updated_at
                    ? format(form?.updated_at, 'dd MMM yyyy')
                    : '---'}
                </div>
              </Col>
            )}
            {showCommonDetails && (
              <Col md={colMdVal}>
                <div style={{fontWeight: 600, fontSize: 14}}>
                  Last Updated by
                </div>
                <div style={{fontSize: 14, fontWeight: 400}}>
                  <button
                    type="button"
                    style={{
                      color: '#1F4A70',
                      textDecoration: 'underline',
                      fontWeight: 400,
                      background: 'none',
                      border: 'none',
                      padding: 0,
                      cursor: 'pointer',
                    }}
                    onClick={() => {
                      // Add navigation or action here if needed
                    }}
                  >
                    {form.created_by === user_id || form.updated_by === user_id
                      ? userName
                      : '---'}
                  </button>
                </div>
              </Col>
            )}
          </Row>
        </div>

        {$duplicateCategoriesMessage}
        {hasRiskAlert && (
          <div className="no-edits-text-red">
            <span>Some of the Jobs</span>&nbsp;risk are reduced significantly.
            Make sure the reasons are mentioned satisfactorily.
          </div>
        )}

        {type === 'risk' && (
          <Row className="mb-4">
            <Col md={6}>
              <Card className="h-390p">
                <Card.Title className="d-flex align-items-center justify-content-between p-16px">
                  <div className="fs-16 fw-600">Risk Assessment Team</div>
                  {!previewOnly && (
                    <Button
                      variant="link"
                      className="underline"
                      style={{
                        textDecoration: 'none',
                        fontWeight: 400,
                        fontSize: 14,
                        color: '#1F4A70',
                        display: 'flex',
                        alignItems: 'center',
                        gap: 4,
                      }}
                      onClick={() => {
                        setEditStep(6);
                        setEditModalTitle('Edit Team Members');
                        setIsEdit(true);
                      }}
                    >
                      <EditFormDetailIcon />
                      Edit Team Members
                    </Button>
                  )}
                </Card.Title>
                <hr />
                <Card.Body className="overflow-auto">
                  {(form as RiskForm).risk_team_member?.length > 0 && (
                    <div className="d-flex flex-column gap-3">
                      {Array.from(
                        {
                          length: Math.ceil(
                            (form as RiskForm).risk_team_member.length / 2,
                          ),
                        },
                        (_, rowIndex) => (
                          <div key={rowIndex} className="d-flex gap-3">
                            {(form as RiskForm).risk_team_member
                              .slice(rowIndex * 2, rowIndex * 2 + 2)
                              .map(member => (
                                <div
                                  key={member.seafarer_id}
                                  className="flex-fill col-6"
                                >
                                  <UsernameProfile
                                    username={member.seafarer_name}
                                    subText={member.seafarer_rank}
                                  />
                                </div>
                              ))}
                          </div>
                        ),
                      )}
                    </div>
                  )}
                </Card.Body>
              </Card>
            </Col>

            <Col md={6}>
              {
                <AddApproverCard
                  riskId={Number(raId)}
                  raLevel={form.ra_level as unknown as RaLevel}
                  raStatus={raStatusLabelToValue[form.status]}
                  existingApprovers={riskApprover}
                  refetchRA={() => refechRA?.()}
                />
              }
            </Col>
          </Row>
        )}
        <Row>
          <Col md={6}>
            <InputComponent
              label="Alternative Considered to carry out above task"
              type="textarea"
              onChange={(
                e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
              ) =>
                setForm({
                  ...form,
                  task_alternative_consideration: e.target.value,
                })
              }
              maxLength={4000}
              rows={4}
              showMaxLength={!previewOnly}
              form={form}
              value={form.task_alternative_consideration}
              name="task_alternative_consideration"
              classes={{
                label: 'fs-16 fw-600',
              }}
              placeholder="List the Alternatives Considered for the Task"
              disabled={previewOnly}
            />
          </Col>
          <Col md={6}>
            <InputComponent
              label="Reason for Rejecting Alternatives"
              type="textarea"
              value={form.task_rejection_reason || ''}
              onChange={(
                e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
              ) => setForm({...form, task_rejection_reason: e.target.value})}
              maxLength={4000}
              rows={4}
              showMaxLength={!previewOnly}
              form={form}
              name="task_rejection_reason"
              classes={{
                label: 'fs-16 fw-600',
              }}
              placeholder="List the Reasons for Rejecting the Alternatives"
              disabled={previewOnly}
            />
          </Col>
        </Row>
        {/* Risk Assessment Category */}
        <div
          className="mb-3"
          style={{
            background: '#fff',
            borderRadius: 6,
            border: '1px solid #CCCCCC',
            padding: 16,
          }}
        >
          <div className="d-flex justify-content-between align-items-center mb-2">
            <span className="d-flex justify-content-between align-items-center">
              <span style={{fontWeight: 600, fontSize: 16, color: '#333333'}}>
                Risk Assessment Category
              </span>
              {isCategoriesSameAsTemplate ? (
                <span className="no-edits-text-yellow">
                  No edits or updates were made to the template data.
                </span>
              ) : null}
            </span>
            {!previewOnly && (
              <Button
                size="sm"
                variant="link"
                className="underline"
                style={{fontSize: 14, fontWeight: 400, color: '#1F4A70'}}
                onClick={() => {
                  setEditStep(2);
                  setEditModalTitle('Edit R.A. Categories');
                  setIsEdit(true);
                }}
              >
                <EditFormDetailIcon />
                Edit Category
              </Button>
            )}
          </div>
          <hr style={{margin: '0 -20px 16px -20px', borderColor: '#DEE2E6'}} />
          <div className="d-flex flex-wrap" style={{gap: '16px'}}>
            {getNamesByIds(riskCategoryList, getCategoryData()).map(name =>
              nameGenrator(name),
            )}
            {/* (
              
              <span key={name} className="badge bg-light text-dark me-2 mb-2">
                {name}
              </span>
            ))} */}
          </div>
        </div>

        {/* Hazard Category */}
        <div
          className="mb-3"
          style={{
            background: '#fff',
            borderRadius: 6,
            border: '1px solid #CCCCCC',
            padding: 16,
          }}
        >
          <div className="d-flex justify-content-between align-items-center mb-2">
            <span className="d-flex justify-content-between align-items-center">
              <span style={{fontWeight: 600, fontSize: 16, color: '#333333'}}>
                Hazard Category
              </span>
              {isHazardsSameAsTemplate ? (
                <span className="no-edits-text-yellow">
                  No edits or updates were made to the template data.
                </span>
              ) : null}
            </span>
            {!previewOnly && (
              <Button
                size="sm"
                variant="link"
                className="underline"
                style={{fontSize: 14, fontWeight: 400, color: '#1F4A70'}}
                onClick={() => {
                  setEditStep(3);
                  setEditModalTitle('Edit Hazard Categories');
                  setIsEdit(true);
                }}
              >
                <EditFormDetailIcon />
                Edit Category
              </Button>
            )}
          </div>
          <hr style={{margin: '0 -20px 16px -20px', borderColor: '#DEE2E6'}} />
          <div className="d-flex flex-wrap" style={{gap: '16px'}}>
            {getNamesByIds(hazardsList, getHazardData()).map(name =>
              nameGenrator(name),
            )}
          </div>
        </div>

        {/* Who & What is At Risk */}
        <div
          className="mb-3"
          style={{
            background: '#fff',
            borderRadius: 6,
            border: '1px solid #CCCCCC',
            padding: 16,
          }}
        >
          <div className="d-flex justify-content-between align-items-center mb-2">
            <div style={{fontWeight: 600, fontSize: 16, color: '#333333'}}>
              Who & What is At Risk
            </div>
            {!previewOnly && (
              <Button
                size="sm"
                variant="link"
                className="underline"
                style={{fontSize: 14, fontWeight: 400, color: '#1F4A70'}}
                onClick={() => {
                  setEditStep(4);
                  setEditModalTitle('Edit Who & What is At Risk');
                  setIsEdit(true);
                }}
              >
                <EditFormDetailIcon />
                Edit Category
              </Button>
            )}
          </div>
          <hr style={{margin: '0 -20px 16px -20px', borderColor: '#DEE2E6'}} />
          <Col md={12} className="mb-3">
            {riskParametersListLoc?.map(param => (
              <div key={param.id}>
                <Row key={param.id}>
                  <div style={{fontWeight: 600, color: '#1F4A70'}}>
                    {param.name.toUpperCase()}
                  </div>
                </Row>
                <Row className="mb-2">
                  <div className="d-flex flex-wrap" style={{gap: '16px'}}>
                    {(getAtRisk(param.id) || []).map(
                      (idOrValue: number | string) => {
                        // If it's a number, look up the name; if string, show as is
                        let displayName = '';
                        if (typeof idOrValue === 'number') {
                          const paramType = riskParametersTypeLoc.find(
                            (p: any) => p.id === param.id,
                          );
                          const option = paramType?.parameters?.find(
                            (opt: any) => opt.id === idOrValue,
                          );
                          displayName = option ? option.name : '';
                        } else {
                          displayName = idOrValue;
                        }
                        if (!displayName.trim()) return null;
                        return (
                          <span
                            key={idOrValue}
                            // className="badge bg-light text-dark me-2 mb-2"
                          >
                            {nameGenrator(displayName)}
                          </span>
                        );
                      },
                    )}
                  </div>
                </Row>
              </div>
            ))}
          </Col>
        </div>

        {/* Hazard & Control Measures Table */}
        <div className="mb-3" style={{background: '#fff', borderRadius: 6}}>
          <div className="d-flex justify-content-between align-items-center mb-3">
            <div>
              <b>Hazard & Control Measures</b>
            </div>
            {!previewOnly && (
              <div style={{display: 'flex', gap: 12}}>
                <Button
                  size="sm"
                  variant="outline-primary"
                  data-testid="button-guidance-table"
                  onClick={() => window.open(GuidancePdf, '_blank')}
                >
                  Guidance Table
                </Button>
                <Button
                  size="sm"
                  variant="outline-primary"
                  className="ms-2"
                  data-testid="button-risk-matrix-table"
                  onClick={() => window.open(RiskMatrixPdf, '_blank')}
                >
                  Risk Matrix Table
                </Button>
                <Button
                  size="sm"
                  variant="primary"
                  className="ms-2"
                  onClick={() => setShowAddJobModal(true)}
                >
                  + Add Job
                </Button>
              </div>
            )}
          </div>
          <InfiniteScrollTable
            key={tableKey} // Force refresh when tableKey changes
            columns={columns}
            data={getJobsArray() as any[]}
            pagination={{
              pageIndex: 0,
              pageSize: getJobsArray().length,
            }}
            tableContainerStyle={{minHeight: 170}}
            sorting={{
              sorting: [],
              // eslint-disable-next-line @typescript-eslint/no-empty-function
              onSortingChange: () => {},
            }}
            // eslint-disable-next-line @typescript-eslint/no-empty-function
            fetchNextPage={() => {}}
          />
        </div>
        <RiskRatingStep
          ref={atRiskRef}
          form={form}
          setForm={setForm}
          // eslint-disable-next-line @typescript-eslint/no-empty-function
          onValidate={() => {}}
          disableHeader
          previewOnly={previewOnly}
        />
      </div>
      {/* Footer Buttons */}
      {previewOnly || disableFooterButtons ? null : (
        <BottomButton
          buttons={
            bottomButtonConfig || [
              {
                title: 'Save & Exit',
                testID: 'form-prj-cancel-btn',
                variant: 'secondary',
                customClass: 'sec-btn fs-14',
                onClick: () => handleSaveToDraft(type === 'risk' ? 8 : 7),
              },
              {
                title: type === 'risk' ? 'Submit' : 'Publish Template',
                testID: 'form-prj-save-btn',
                variant: 'primary',
                customClass: 'primary-btn fs-14',
                onClick: () => handlePublish(),
              },
            ]
          }
        />
      )}
      {isEdit && (
        <EditTemplateModal
          onClose={onClose}
          title={editModalTitle}
          step={editStep}
          form={form}
          setForm={setForm}
          jobId={selectedJobIdx}
          type={type}
        />
      )}
      {showDeleteJobModal && (
        <DeleteJobModal
          onClose={onDeleteClose}
          jobId={selectedJobIdx}
          form={form}
          setForm={setForm}
          type={type}
        />
      )}
      {showAddJobModal && (
        <AddJobModal
          onClose={async (shouldSave?: boolean) => {
            setShowAddJobModal(false);
            // Only call handlePreviewPublush if user clicked Save and allowSaveOnModalClose is true
            if (shouldSave && allowSaveOnModalClose) {
              await handlePreviewPublush();
            }
          }}
          form={form}
          setForm={setForm}
          type={type}
        />
      )}
      {showRASubmitModal && (
        <SubmitLevel1RAModal
          onConfirm={() => handlePreviewPublush()}
          setForm={setForm}
          show={showRASubmitModal}
          onClose={() => setShowRASubmitModal(false)}
        />
      )}
    </div>
  );
};

export default PreviewFormDetails;
